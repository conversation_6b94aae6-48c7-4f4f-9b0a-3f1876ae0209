"use client";

import { useAuth } from "@/lib/auth-context";
import { LoginForm } from "./LoginForm";
import { Loader2 } from "lucide-react";

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function AuthGuard({ 
  children, 
  fallback,
  requireAuth = true,
  redirectTo 
}: AuthGuardProps) {
  const { user, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !user) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <LoginForm 
          title="Authentication Required"
          description="Please sign in to access this content"
          redirectTo={redirectTo}
        />
      </div>
    );
  }

  // If authentication is not required or user is authenticated
  return <>{children}</>;
}

"use client";

import { useAuth } from "@/lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LogIn, Loader2 } from "lucide-react";
import { useState } from "react";

interface LoginFormProps {
  title?: string;
  description?: string;
  redirectTo?: string;
  className?: string;
}

export function LoginForm({ 
  title = "Welcome to TraceStack",
  description = "Sign in to access your coding journey dashboard",
  redirectTo,
  className = ""
}: LoginFormProps) {
  const { user, loading, signInWithGoogle } = useAuth();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const handleSignIn = async () => {
    setIsAuthenticating(true);
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error("Error signing in:", error);
      setIsAuthenticating(false);
    }
  };

  // If user is already authenticated, show success message
  if (user) {
    return (
      <Card className={`w-full max-w-md mx-auto ${className}`}>
        <CardHeader className="text-center">
          <CardTitle className="text-green-600">Welcome Back!</CardTitle>
          <CardDescription>
            You are signed in as {user.email}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-sm text-muted-foreground">
            You can now access all features of TraceStack.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={handleSignIn}
          disabled={loading || isAuthenticating}
          className="w-full"
          size="lg"
        >
          {loading || isAuthenticating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              {loading ? "Loading..." : "Signing in..."}
            </>
          ) : (
            <>
              <LogIn className="h-4 w-4 mr-2" />
              Sign in with Google
            </>
          )}
        </Button>
        
        <div className="text-center text-sm text-muted-foreground">
          <p>
            By signing in, you agree to our terms of service and privacy policy.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

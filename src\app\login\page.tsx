"use client";

export default function LoginPage() {
  const { user } = useAuth();
  const router = useRouter();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (user) {
      router.push("/dashboard");
    }
  }, [user, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-black p-4">
      <LoginForm
        title="Welcome to TraceStack"
        description="Sign in to access your coding journey dashboard"
        className="bg-gray-900 border border-gray-800"
      />
    </div>
  );
}

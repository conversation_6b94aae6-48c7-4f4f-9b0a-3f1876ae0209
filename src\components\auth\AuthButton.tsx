"use client";

import { useAuth } from "@/lib/auth-context";
import { Button } from "@/components/ui/button";
import { LogIn, LogOut, Loader2 } from "lucide-react";
import { useState } from "react";

interface AuthButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
  showText?: boolean;
}

export function AuthButton({ 
  variant = "default", 
  size = "default", 
  className = "",
  showText = true 
}: AuthButtonProps) {
  const { user, loading, signInWithGoogle, signOut } = useAuth();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const handleAuth = async () => {
    if (user) {
      // Sign out
      setIsAuthenticating(true);
      try {
        await signOut();
      } catch (error) {
        console.error("Error signing out:", error);
      } finally {
        setIsAuthenticating(false);
      }
    } else {
      // Sign in
      setIsAuthenticating(true);
      try {
        await signInWithGoogle();
      } catch (error) {
        console.error("Error signing in:", error);
        setIsAuthenticating(false);
      }
    }
  };

  if (loading) {
    return (
      <Button variant={variant} size={size} className={className} disabled>
        <Loader2 className="h-4 w-4 animate-spin" />
        {showText && <span className="ml-2">Loading...</span>}
      </Button>
    );
  }

  return (
    <Button 
      variant={variant} 
      size={size} 
      className={className}
      onClick={handleAuth}
      disabled={isAuthenticating}
    >
      {isAuthenticating ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : user ? (
        <LogOut className="h-4 w-4" />
      ) : (
        <LogIn className="h-4 w-4" />
      )}
      {showText && (
        <span className="ml-2">
          {isAuthenticating 
            ? "Processing..." 
            : user 
              ? "Sign Out" 
              : "Sign In with Google"
          }
        </span>
      )}
    </Button>
  );
}
